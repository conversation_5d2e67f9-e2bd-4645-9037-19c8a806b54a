Rails.application.routes.draw do
  # API routes
  namespace :api do
    resources :sites do
      resources :health_checks, only: [ :index, :create ]
      resources :alerts, only: [ :index, :create ]
      get :failed_jobs, on: :member
    end

    resources :health_checks, only: [ :show, :index ]
    resources :alerts, only: [ :update, :destroy ]
  end

  # Test endpoint to simulate failed jobs from monitored sites
  get "/failed_jobs", to: "test_failed_jobs#show"
end
