# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose

version: '3.8'

services:
  api:
    environment:
      - RAILS_ENV=development
      - RAILS_LOG_LEVEL=debug
    volumes:
      - .:/app
      - bundle_cache:/usr/local/bundle
    command: ["bundle", "exec", "rails", "server", "-b", "0.0.0.0", "-p", "3100"]

  frontend:
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3100/api
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    command: ["npm", "run", "dev"]

  # Remove nginx in development
  nginx:
    profiles:
      - production

volumes:
  bundle_cache:
  frontend_node_modules:
