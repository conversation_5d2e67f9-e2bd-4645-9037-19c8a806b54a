# Production configuration for docker-compose
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

version: '3.8'

services:
  api:
    container_name: cdr_monitor_api
    environment:
      - RAILS_ENV=production
      - RAILS_SERVE_STATIC_FILES=true
      - RAILS_LOG_TO_STDOUT=true
      - SECRET_KEY_BASE=${SECRET_KEY_BASE:-$(openssl rand -hex 64)}
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  frontend:
    container_name: cdr_monitor_frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:3100/api
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  nginx:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.25'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
