version: '3.8'

services:
  # Rails API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: cdr_monitor_api
    ports:
      - "3100:3100"
    environment:
      - RAILS_ENV=production
      - RAILS_SERVE_STATIC_FILES=true
      - RAILS_LOG_TO_STDOUT=true
      - DATABASE_URL=sqlite3:storage/production.sqlite3
    volumes:
      - api_data:/app/storage
      - api_logs:/app/log
    networks:
      - cdr_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Next.js Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=http://localhost:3100/api
    container_name: cdr_monitor_frontend
    ports:
      - "3200:3200"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:3100/api
      - PORT=3200
      - HOSTNAME=0.0.0.0
    depends_on:
      api:
        condition: service_healthy
    networks:
      - cdr_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3200/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: cdr_monitor_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - frontend
    networks:
      - cdr_network
    restart: unless-stopped

volumes:
  api_data:
    driver: local
  api_logs:
    driver: local

networks:
  cdr_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
