import axios from 'axios';

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3100/api',
});

// TypeScript interfaces based on Rails models
export interface Site {
  id: number;
  name: string;
  ip_address: string;
  port: number;
  status: 'up' | 'down' | 'unknown';
  last_check_at: string | null;
  response_time: number | null;
  created_at: string;
  updated_at: string;
  latest_health_check?: HealthCheck | null;
  health_checks_count?: number;
}

export interface Alert {
  id: number;
  site_id: number;
  alert_type: 'downtime' | 'performance';
  email: string;
  is_active: boolean;
  last_sent_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface HealthCheck {
  id: number;
  site_id: number;
  status: 'up' | 'down' | 'unknown';
  response_time: number;
  jobs_done: number;
  jobs_pending: number;
  jobs_failed: number;
  jobs_queued: number;
  last_sync_at: string | null;
  visitor_count: number;
  created_at: string;
  updated_at: string;
}

export interface FailedJob {
  id: number;
  job_class: string;
  error_message: string;
  failed_at: string;
  retry_count: number;
  queue_name: string;
  arguments: Record<string, unknown>;
  priority: number;
  scheduled_at: string | null;
  finished_at?: string | null;
  active_job_id: string;
}

export interface FailedJobsResponse {
  site_id: number;
  site_name: string;
  failed_jobs: FailedJob[];
  total_count: number;
  error?: string;
}

// Sites API
export const sitesApi = {
  getAll: async (): Promise<Site[]> => {
    const response = await api.get('/sites');
    return response.data;
  },

  getById: async (id: number): Promise<Site> => {
    const response = await api.get(`/sites/${id}`);
    return response.data;
  },

  create: async (data: Omit<Site, 'id' | 'created_at' | 'updated_at' | 'latest_health_check' | 'health_checks_count'>): Promise<Site> => {
    const response = await api.post('/sites', { site: data });
    return response.data;
  },

  update: async (id: number, data: Partial<Site>): Promise<Site> => {
    const response = await api.put(`/sites/${id}`, { site: data });
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/sites/${id}`);
  },

  getFailedJobs: async (id: number): Promise<FailedJobsResponse> => {
    const response = await api.get(`/sites/${id}/failed_jobs`);
    return response.data;
  },
};

// Alerts API
export const alertsApi = {
  getBySiteId: async (siteId: number): Promise<Alert[]> => {
    const response = await api.get(`/sites/${siteId}/alerts`);
    return response.data;
  },

  create: async (siteId: number, data: Omit<Alert, 'id' | 'site_id' | 'created_at' | 'updated_at'>): Promise<Alert> => {
    const response = await api.post(`/sites/${siteId}/alerts`, { alert: data });
    return response.data;
  },

  update: async (id: number, data: Partial<Alert>): Promise<Alert> => {
    const response = await api.put(`/alerts/${id}`, { alert: data });
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/alerts/${id}`);
  },
};

// Health Checks API
export const healthChecksApi = {
  getAll: async (params?: { start_date?: string; end_date?: string }): Promise<HealthCheck[]> => {
    const response = await api.get('/health_checks', { params });
    return response.data;
  },

  getAllWithDateRange: async (startDate: Date | null, endDate: Date | null): Promise<HealthCheck[]> => {
    const params: { start_date?: string; end_date?: string } = {};
    if (startDate) {
      params.start_date = startDate.toISOString();
    }
    if (endDate) {
      params.end_date = endDate.toISOString();
    }
    const response = await api.get('/health_checks', { params });
    return response.data;
  },

  getBySiteId: async (siteId: number, params?: { start_date?: string; end_date?: string }): Promise<HealthCheck[]> => {
    const response = await api.get(`/sites/${siteId}/health_checks`, { params });
    return response.data;
  },

  getBySiteIdWithDateRange: async (siteId: number, startDate: Date | null, endDate: Date | null): Promise<HealthCheck[]> => {
    const params: { start_date?: string; end_date?: string } = {};
    if (startDate) {
      params.start_date = startDate.toISOString();
    }
    if (endDate) {
      params.end_date = endDate.toISOString();
    }
    const response = await api.get(`/sites/${siteId}/health_checks`, { params });
    return response.data;
  },

  getById: async (id: number): Promise<HealthCheck> => {
    const response = await api.get(`/health_checks/${id}`);
    return response.data;
  },

  create: async (siteId: number, data: Omit<HealthCheck, 'id' | 'site_id' | 'created_at' | 'updated_at'>): Promise<HealthCheck> => {
    const response = await api.post(`/sites/${siteId}/health_checks`, { health_check: data });
    return response.data;
  },
};

export default api;
