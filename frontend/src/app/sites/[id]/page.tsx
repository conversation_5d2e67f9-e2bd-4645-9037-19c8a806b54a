'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { sitesApi, healthChecksApi } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DateRangeFilter from '@/components/DateRangeFilter';
import FailedJobsModal from '@/components/FailedJobsModal';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  AreaChart, Area, PieChart, Pie, Cell
} from 'recharts';
import { format, startOfDay, endOfDay, differenceInMinutes, differenceInHours } from 'date-fns';
import {
  ArrowLeft, Activity, Clock,
  CheckCircle, AlertTriangle, Server, Users, Zap, RefreshCw
} from 'lucide-react';
import Link from 'next/link';



export default function SiteDetailPage() {
  const params = useParams();
  const siteId = parseInt(params.id as string);

  // Default to last 7 days
  const [startDate, setStartDate] = useState<Date | null>(startOfDay(new Date()));
  const [endDate, setEndDate] = useState<Date | null>(endOfDay(new Date()));
  const [showFailedJobsModal, setShowFailedJobsModal] = useState(false);

  const { data: site, isLoading: siteLoading, refetch: refetchSite } = useQuery({
    queryKey: ['sites', siteId],
    queryFn: () => sitesApi.getById(siteId),
  });

  const { data: healthChecks = [], isLoading: healthChecksLoading, refetch: refetchHealthChecks } = useQuery({
    queryKey: ['healthChecks', siteId, startDate, endDate],
    queryFn: () => healthChecksApi.getBySiteIdWithDateRange(siteId, startDate, endDate),
  });

  const handleDateRangeChange = (newStartDate: Date | null, newEndDate: Date | null) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
  };

  const distanceInWordsToNow = (date: Date, options?: { addSuffix?: boolean }) => {
    if(options) {}
    const diffInMinutes = differenceInMinutes(new Date(), date);
    const diffInHours = differenceInHours(new Date(), date);
  
    if (diffInMinutes < 1) {
      return 'just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
      } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  const handleRefresh = async () => {
    await Promise.all([refetchSite(), refetchHealthChecks()]);
  };

  if (siteLoading || healthChecksLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!site) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Site not found</h3>
        </div>
      </DashboardLayout>
    );
  }

  // Prepare chart data
  const chartData = healthChecks
    .slice(-20) // Last 20 checks
    .map(check => ({
      time: format(new Date(check.created_at), 'HH:mm'),
      responseTime: check.response_time || 0,
      status: check.status,
      jobsDone: check.jobs_done || 0,
      jobsPending: check.jobs_pending || 0,
      jobsFailed: check.jobs_failed || 0,
      jobsQueued: check.jobs_queued || 0,
      visitorCount: check.visitor_count || 0,
    }));

  // Calculate statistics
  const avgResponseTime = healthChecks.reduce((sum, check) => sum + (check.response_time || 0), 0) / healthChecks.length;
  const uptime = (healthChecks.filter(check => check.status === 'up').length / healthChecks.length) * 100;
  
  const latestCheck = healthChecks[0];
  const lastestSuccessfulCheck = healthChecks.find(check => check.status === 'up');
  const totalJobs = latestCheck ? 
    (latestCheck.jobs_done || 0) + (latestCheck.jobs_pending || 0) + 
    (latestCheck.jobs_failed || 0) + (latestCheck.jobs_queued || 0) : 0;

  const jobsData = latestCheck ? [
    { name: 'Done', value: latestCheck.jobs_done || 0, color: '#10B981' },
    { name: 'Pending', value: latestCheck.jobs_pending || 0, color: '#F59E0B' },
    { name: 'Failed', value: latestCheck.jobs_failed || 0, color: '#EF4444' },
    { name: 'Queued', value: latestCheck.jobs_queued || 0, color: '#8B5CF6' },
  ] : [];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 text-black pointer-cursor">
          <div className="flex items-center space-x-4">
            <Link
              href="/sites"
              className="p-2 rounded-md hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <button
              onClick={handleRefresh}
              disabled={siteLoading || healthChecksLoading}
              className="p-2 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed text-black pointer-cursor"
              title="Refresh data"
            >
              <RefreshCw className={`h-5 w-5 ${(siteLoading || healthChecksLoading) ? 'animate-spin' : ''}`} />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{site.name}</h1>
              <p className="text-sm text-gray-600">{site.ip_address}:{site.port}</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 ">
            <DateRangeFilter
              startDate={startDate}
              endDate={endDate}
              onDateRangeChange={handleDateRangeChange}
            />

            <div className="flex items-center space-x-2">
              {site.status === 'up' && <CheckCircle className="h-6 w-6 text-green-500" />}
              {site.status === 'down' && <AlertTriangle className="h-6 w-6 text-red-500" />}
              {site.status === 'unknown' && <Clock className="h-6 w-6 text-yellow-500" />}
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                site.status === 'up' ? 'bg-green-100 text-green-800' :
                site.status === 'down' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {site.status.toUpperCase()}
              </span>
                {site.status === 'down' && lastestSuccessfulCheck && (
                  <span className="text-xs text-gray-600">
                    Last Seen {distanceInWordsToNow(new Date(lastestSuccessfulCheck.created_at), { addSuffix: true })}
                  </span>
                ) }
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Activity className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Uptime (Last 10)</dt>
                    <dd className="text-lg font-medium text-gray-900">{uptime.toFixed(1)}%</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Zap className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                    <dd className="text-lg font-medium text-gray-900">{avgResponseTime.toFixed(0)}ms</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Server className="h-6 w-6 text-purple-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Jobs</dt>
                    <dd className="text-lg font-medium text-gray-900">{totalJobs}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Visits</dt>
                    <dd className="text-lg font-medium text-gray-900">{latestCheck?.visitor_count || 0}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Response Time Chart */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Response Time Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip 
                  formatter={(value) => [`${value}ms`, 'Response Time']}
                  labelFormatter={(label) => `Time: ${label}`}
                />
                <Line 
                  type="monotone" 
                  dataKey="responseTime" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Jobs Distribution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Current Jobs Distribution</h3>
              {latestCheck && latestCheck.jobs_failed! > 0 && (
                <button
                  onClick={() => setShowFailedJobsModal(true)}
                  className="inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  View Failed Jobs ({latestCheck.jobs_failed})
                </button>
              )}
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={jobsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {jobsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Jobs Over Time */}
        </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Jobs Over Time</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="jobsDone" stackId="1" stroke="#10B981" fill="#10B981" />
                <Area type="monotone" dataKey="jobsPending" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
                <Area type="monotone" dataKey="jobsFailed" stackId="1" stroke="#EF4444" fill="#EF4444" />
                <Area type="monotone" dataKey="jobsQueued" stackId="1" stroke="#8B5CF6" fill="#8B5CF6" />
              </AreaChart>
            </ResponsiveContainer>
          </div>

        {/* Recent Health Checks Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Health Checks</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Latest health check results for this site
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Response Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jobs
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Visits
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {healthChecks.slice(0, 10).map((check) => (
                  <tr key={check.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {format(new Date(check.created_at), 'MMM dd, HH:mm:ss')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        check.status === 'up' ? 'bg-green-100 text-green-800' :
                        check.status === 'down' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {check.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {check.response_time ? `${check.response_time.toFixed(0)}ms` : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="text-xs">
                        <div>Done: {check.jobs_done || 0}</div>
                        <div>Failed: {check.jobs_failed || 0}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {check.visitor_count || 0}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Failed Jobs Modal */}
        <FailedJobsModal
          siteId={siteId}
          siteName={site.name}
          isOpen={showFailedJobsModal}
          onClose={() => setShowFailedJobsModal(false)}
        />
      </div>
    </DashboardLayout>
  );
}
