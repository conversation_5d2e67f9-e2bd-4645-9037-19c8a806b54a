# CDR Site Monitor - Docker Deployment Guide

This guide explains how to deploy the CDR Site Monitor application using Docker and Docker Compose.

## Architecture

The Docker setup consists of three main services:

- **API Service** (Rails): Runs on port 3100
- **Frontend Service** (Next.js): Runs on port 3200  
- **Nginx Proxy**: Runs on port 80 (production only)

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

## Quick Start

### Development Environment

```bash
# Start development environment
./docker-deploy.sh development up

# Or using docker-compose directly
docker-compose up --build
```

**Access URLs:**
- Frontend: http://localhost:3200
- API: http://localhost:3100
- Health Check: http://localhost:3100/up

### Production Environment

```bash
# Start production environment
./docker-deploy.sh production up

# Or using docker-compose directly
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
```

**Access URLs:**
- Main Application: http://localhost (via Nginx)
- Frontend Direct: http://localhost:3200
- API Direct: http://localhost:3100

## Configuration

### Environment Variables

Copy the example environment file and customize:

```bash
cp .env.example .env
```

Key variables:
- `RAILS_ENV`: Rails environment (development/production)
- `NEXT_PUBLIC_API_URL`: API URL for frontend
- `SECRET_KEY_BASE`: Rails secret key (generate with `openssl rand -hex 64`)

### Docker Compose Files

- `docker-compose.yml`: Base configuration
- `docker-compose.override.yml`: Development overrides (auto-loaded)
- `docker-compose.prod.yml`: Production configuration

## Deployment Commands

### Using the Deployment Script

```bash
# Development
./docker-deploy.sh development up     # Start services
./docker-deploy.sh development down   # Stop services
./docker-deploy.sh development logs   # View logs
./docker-deploy.sh development status # Check status

# Production
./docker-deploy.sh production up      # Start services
./docker-deploy.sh production down    # Stop services
./docker-deploy.sh production logs    # View logs
./docker-deploy.sh production restart # Restart services
```

### Using Docker Compose Directly

```bash
# Development
docker-compose up --build -d          # Start in background
docker-compose down                    # Stop services
docker-compose logs -f                 # Follow logs
docker-compose ps                      # Check status

# Production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f
```

## Service Details

### API Service (Rails)

**Dockerfile:** `Dockerfile.api`
**Port:** 3100
**Health Check:** `/up`

Features:
- Ruby 3.2 base image
- SQLite database (persistent volume)
- Asset precompilation
- Database migrations and seeding
- Health checks

### Frontend Service (Next.js)

**Dockerfile:** `frontend/Dockerfile`
**Port:** 3200
**Health Check:** `/`

Features:
- Node.js 18 Alpine base image
- Standalone output for optimal Docker deployment
- Multi-stage build for smaller image size
- Environment variable configuration
- Static asset optimization

### Nginx Proxy (Production)

**Port:** 80
**Configuration:** `nginx.conf`

Features:
- Reverse proxy for API and frontend
- Static asset caching
- Security headers
- Gzip compression
- Health checks

## Volumes and Persistence

### Persistent Volumes

- `api_data`: Rails storage directory
- `api_logs`: Rails log files

### Development Volumes

- `bundle_cache`: Ruby gem cache
- `frontend_node_modules`: Node.js modules cache

## Networking

**Network:** `cdr_network` (**********/16)

Services communicate internally using service names:
- API accessible at `http://api:3100`
- Frontend accessible at `http://frontend:3200`

## Health Checks

All services include health checks:
- **API**: `curl -f http://localhost:3100/up`
- **Frontend**: `curl -f http://localhost:3200/`
- **Nginx**: `curl -f http://localhost:8080/health`

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3100, 3200, and 80 are available
2. **Permission issues**: Check Docker daemon permissions
3. **Build failures**: Clear Docker cache with `docker system prune`

### Debugging Commands

```bash
# View service logs
docker-compose logs [service_name]

# Execute commands in running container
docker-compose exec api bash
docker-compose exec frontend sh

# Check container status
docker-compose ps

# View resource usage
docker stats
```

### Database Issues

```bash
# Reset database
docker-compose exec api bundle exec rails db:drop db:create db:migrate db:seed

# Access Rails console
docker-compose exec api bundle exec rails console
```

## Scaling (Production)

The production configuration supports scaling:

```bash
# Scale API service
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --scale api=3

# Scale frontend service  
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --scale frontend=2
```

## Security Considerations

- Change default `SECRET_KEY_BASE` in production
- Use HTTPS in production (configure SSL certificates)
- Regularly update base images
- Monitor container logs
- Implement proper backup strategy

## Monitoring

### Container Health

```bash
# Check health status
docker-compose ps

# View health check logs
docker inspect --format='{{json .State.Health}}' container_name
```

### Application Monitoring

- API Health: http://localhost:3100/up
- Application Logs: `docker-compose logs -f`
- Resource Usage: `docker stats`

## Backup and Recovery

### Database Backup

```bash
# Backup SQLite database
docker-compose exec api cp storage/production.sqlite3 /tmp/backup.sqlite3
docker cp container_name:/tmp/backup.sqlite3 ./backup.sqlite3
```

### Volume Backup

```bash
# Backup persistent volumes
docker run --rm -v cdr_monitor_api_data:/data -v $(pwd):/backup alpine tar czf /backup/api_data.tar.gz -C /data .
```

## Updates and Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull

# Rebuild and restart
./docker-deploy.sh production restart
```

### Updating Dependencies

```bash
# Rebuild images
docker-compose build --no-cache

# Restart services
docker-compose up -d
```
