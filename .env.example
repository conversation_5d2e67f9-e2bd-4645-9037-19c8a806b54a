# CDR Site Monitor - Environment Variables

# Rails Configuration
RAILS_ENV=production
SECRET_KEY_BASE=your_secret_key_here
DATABASE_URL=sqlite3:storage/production.sqlite3

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3100/api
NODE_ENV=production

# Docker Configuration
COMPOSE_PROJECT_NAME=cdr_monitor

# Nginx Configuration (if using SSL)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Optional: External Database (PostgreSQL)
# DATABASE_URL=postgresql://username:password@host:port/database

# Optional: Redis (for caching/sessions)
# REDIS_URL=redis://localhost:6379/0

# Optional: Email Configuration
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
