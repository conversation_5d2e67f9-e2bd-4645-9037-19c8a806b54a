#!/bin/bash

# CDR Site Monitor - Docker Deployment Script
# This script builds and deploys the application using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
ENVIRONMENT=${1:-development}
ACTION=${2:-up}

echo
print_status "CDR Site Monitor - Docker Deployment"
print_status "===================================="
echo

print_status "Configuration:"
echo "  Environment: $ENVIRONMENT"
echo "  Action: $ACTION"
echo

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    print_warning "Please edit .env file with your configuration before running in production"
fi

# Function to deploy development environment
deploy_development() {
    print_status "Deploying development environment..."
    
    # Stop any running containers
    docker-compose down
    
    # Build and start services
    docker-compose up --build -d
    
    print_success "Development environment deployed!"
    print_status "Services:"
    echo "  API: http://localhost:3100"
    echo "  Frontend: http://localhost:3200"
    echo "  Health Check: http://localhost:3100/up"
}

# Function to deploy production environment
deploy_production() {
    print_status "Deploying production environment..."
    
    # Stop any running containers
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
    
    # Build and start services
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
    
    print_success "Production environment deployed!"
    print_status "Services:"
    echo "  Nginx Proxy: http://localhost:80"
    echo "  API: http://localhost:3100"
    echo "  Frontend: http://localhost:3200"
    echo "  Health Check: http://localhost:3100/up"
}

# Function to show logs
show_logs() {
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f
    else
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    print_success "Services stopped"
}

# Function to show status
show_status() {
    print_status "Service status:"
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# Main execution
case $ACTION in
    up|start)
        if [ "$ENVIRONMENT" = "production" ]; then
            deploy_production
        else
            deploy_development
        fi
        ;;
    down|stop)
        stop_services
        ;;
    logs)
        show_logs
        ;;
    status|ps)
        show_status
        ;;
    restart)
        stop_services
        sleep 2
        if [ "$ENVIRONMENT" = "production" ]; then
            deploy_production
        else
            deploy_development
        fi
        ;;
    *)
        echo "Usage: $0 [environment] [action]"
        echo "  environment: development (default) | production"
        echo "  action: up (default) | down | logs | status | restart"
        echo
        echo "Examples:"
        echo "  $0                          # Start development environment"
        echo "  $0 development up           # Start development environment"
        echo "  $0 production up            # Start production environment"
        echo "  $0 development logs         # Show development logs"
        echo "  $0 production down          # Stop production environment"
        exit 1
        ;;
esac

echo
print_status "Docker deployment completed!"

# Show running containers
echo
print_status "Running containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
